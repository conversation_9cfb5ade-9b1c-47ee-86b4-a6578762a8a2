import os

import instructor
import logfire
import uvicorn
from dotenv import load_dotenv
from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from instructor.multimodal import Image
from openai import AsyncOpenAI
from pydantic import BaseModel, Field

load_dotenv(dotenv_path=".env.development", override=True)


class ImageInput(BaseModel):
    content: str = Field(..., description="The content of the image.")


class CodeOCRResponse(BaseModel):
    language: str = Field(..., description="The programming language.")
    code: str = Field(..., description="The extracted code.")


openai_client = AsyncOpenAI(
    api_key=os.getenv("OPENAI_API_KEY"),
    base_url=os.getenv("OPENAI_API_BASE"),
)
client = instructor.from_openai(
    openai_client,
    mode=instructor.Mode.JSON,
)

app = FastAPI(
    title="CodeOCR API",
    description="Convert code screenshot to editable code.",
)
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, replace with specific origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
logfire.configure(pydantic_plugin=logfire.PydanticPlugin(record="all"))
logfire.instrument_openai(openai_client)
logfire.instrument_fastapi(app)


@app.get("/")
async def root():
    """Health check endpoint."""
    return {"status": "ok", "message": "CodeOCR API is running successfully!"}


@app.post("/api/codeocr", response_model=CodeOCRResponse)
async def codeocr(image: ImageInput) -> CodeOCRResponse:
    response = await client.chat.completions.create(
        model=os.getenv("MODEL_NAME"),
        response_model=CodeOCRResponse,
        messages=[
            {
                "role": "user",
                "content": [
                    "Extract code from this image",
                    Image.autodetect(image.content),
                ],
            },
        ],
    )
    return response


if __name__ == "__main__":
    uvicorn.run("main:app", host="127.0.0.1", port=8000, reload=True)
